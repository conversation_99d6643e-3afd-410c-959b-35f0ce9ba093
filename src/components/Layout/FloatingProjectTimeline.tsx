import React, { useState } from 'react';
import { 
  Clock, 
  Calendar, 
  ChevronUp, 
  ChevronDown, 
  AlertTriangle,
  CheckCircle,
  X
} from 'lucide-react';

/**
 * Floating Project Timeline Component
 * 
 * A floating widget that shows project timeline information
 * - Collapsible/expandable
 * - Always visible on screen
 * - Shows progress, milestones, and key dates
 */
const FloatingProjectTimeline: React.FC = () => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isVisible, setIsVisible] = useState(true);

  if (!isVisible) return null;

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <div className="bg-white rounded-lg shadow-lg border border-gray-200 w-80">
        {/* Header */}
        <div 
          className="flex items-center justify-between p-3 bg-blue-600 text-white rounded-t-lg cursor-pointer"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          <div className="flex items-center">
            <Calendar className="h-4 w-4 mr-2" />
            <span className="font-medium text-sm">Project Timeline</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-xs bg-blue-500 px-2 py-1 rounded">42%</span>
            <button
              onClick={(e) => {
                e.stopPropagation();
                setIsVisible(false);
              }}
              className="text-blue-200 hover:text-white"
            >
              <X className="h-4 w-4" />
            </button>
            {isExpanded ? (
              <ChevronDown className="h-4 w-4" />
            ) : (
              <ChevronUp className="h-4 w-4" />
            )}
          </div>
        </div>

        {/* Collapsed View */}
        {!isExpanded && (
          <div className="p-3 bg-gray-50 rounded-b-lg">
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center text-gray-600">
                <Clock className="h-3 w-3 mr-1 text-orange-500" />
                <span>Next: Inverter Delivery</span>
              </div>
              <span className="font-medium text-orange-600">5 days</span>
            </div>
          </div>
        )}

        {/* Expanded View */}
        {isExpanded && (
          <div className="p-4 space-y-4">
            {/* Project Dates */}
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-500 text-xs">Start Date</span>
                <div className="font-medium text-gray-900">Apr 12, 2025</div>
              </div>
              <div>
                <span className="text-gray-500 text-xs">End Date</span>
                <div className="font-medium text-gray-900">Dec 30, 2025</div>
              </div>
            </div>

            {/* Progress Bar */}
            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-gray-700">Overall Progress</span>
                <span className="text-sm font-bold text-blue-600">42%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                  style={{ width: '42%' }}
                ></div>
              </div>
              <div className="text-xs text-gray-500 mt-1">
                On track for completion
              </div>
            </div>

            {/* Current Phase */}
            <div className="border-t pt-3">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">Current Phase</span>
                <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Active</span>
              </div>
              <div className="text-sm text-gray-900">Construction Phase</div>
              <div className="text-xs text-gray-500">Materials procurement and installation</div>
            </div>

            {/* Next Milestone */}
            <div className="border-t pt-3">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">Next Milestone</span>
                <span className="text-sm font-bold text-orange-600">5 days</span>
              </div>
              <div className="flex items-center">
                <AlertTriangle className="h-4 w-4 text-orange-500 mr-2" />
                <div>
                  <div className="text-sm font-medium text-gray-900">Inverter Delivery</div>
                  <div className="text-xs text-gray-500">Critical component for Phase 1 completion</div>
                </div>
              </div>
            </div>

            {/* Recent Milestones */}
            <div className="border-t pt-3">
              <span className="text-sm font-medium text-gray-700 mb-2 block">Recent Milestones</span>
              <div className="space-y-2">
                <div className="flex items-center">
                  <CheckCircle className="h-3 w-3 text-green-500 mr-2" />
                  <div className="text-xs">
                    <span className="text-gray-900">Foundation Complete</span>
                    <span className="text-gray-500 ml-2">3 days ago</span>
                  </div>
                </div>
                <div className="flex items-center">
                  <CheckCircle className="h-3 w-3 text-green-500 mr-2" />
                  <div className="text-xs">
                    <span className="text-gray-900">Permits Approved</span>
                    <span className="text-gray-500 ml-2">1 week ago</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Team Info */}
            <div className="border-t pt-3 text-center">
              <div className="text-xs text-gray-500">
                <div>1.2 MW Capacity • 8 team members</div>
                <div className="mt-1">SunValley Commercial Campus</div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default FloatingProjectTimeline;
