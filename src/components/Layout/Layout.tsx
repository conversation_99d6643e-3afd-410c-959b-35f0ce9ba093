import React, { useState } from 'react';
import { useLocation } from 'react-router-dom';
import Sidebar from './Sidebar';
import Header from './Header';
import QuickActions from '../Common/QuickActions';
import AIAssistant from '../Common/AIAssistant';
import { Menu, X } from 'lucide-react';

interface LayoutProps {
  children: React.ReactNode;
}

/**
 * Main Layout Component
 * 
 * Provides the overall application layout with:
 * - Responsive sidebar navigation
 * - Header with site/flight selection
 * - Main content area
 * - Mobile-friendly collapsible navigation
 */
const Layout: React.FC<LayoutProps> = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const location = useLocation();

  // Get page title based on current route
  const getPageTitle = () => {
    switch (location.pathname) {
      case '/':
        return 'Project Summary';
      case '/layout':
        return 'Site Layout';
      case '/simulation':
        return 'Performance Simulation';
      case '/risk':
        return 'Risk Assessment';
      case '/bom':
        return 'Materials & Procurement';
      case '/reports':
        return 'Reports & Analytics';
      case '/operations':
        return 'Operations & Maintenance';
      case '/degradation':
        return 'Asset Degradation Overview';
      case '/admin':
        return 'System Administration';
      default:
        return 'SolarPro';
    }
  };

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-gray-600 bg-opacity-75 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={`
        fixed inset-y-0 left-0 z-50 w-64 transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0
        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
      `}>
        <Sidebar onClose={() => setSidebarOpen(false)} />
      </div>

      {/* Main content area */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <Header
          title={getPageTitle()}
          onMenuClick={() => setSidebarOpen(true)}
        />

        {/* Main content */}
        <main className="flex-1 overflow-auto">
          <div className="p-4">
            {children}
          </div>
        </main>
      </div>

      {/* Quick Actions and AI Assistant */}
      <QuickActions />
      <AIAssistant />
    </div>
  );
};

export default Layout;
