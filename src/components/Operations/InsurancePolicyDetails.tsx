import React from 'react';
import { 
  X, 
  Calendar, 
  Building, 
  FileText, 
  Download, 
  AlertTriangle,
  CheckCircle,
  DollarSign,
  Shield,
  Clock
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card';
import { Badge } from '../ui/Badge';
import { InsurancePolicy } from '../../types';

interface InsurancePolicyDetailsProps {
  policy: InsurancePolicy;
  onClose: () => void;
  onEditPolicy: (policy: InsurancePolicy) => void;
  onFileClaim: (policy: InsurancePolicy) => void;
}

/**
 * Insurance Policy Details Modal Component
 * 
 * Displays comprehensive insurance policy information including:
 * - Policy details and coverage
 * - Premium and payment information
 * - Coverage breakdown
 * - Claims history
 * - Document management
 */
const InsurancePolicyDetails: React.FC<InsurancePolicyDetailsProps> = ({
  policy,
  onClose,
  onEditPolicy,
  onFileClaim
}) => {
  const getDaysUntilRenewal = (renewalDate: string) => {
    const days = Math.ceil((new Date(renewalDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));
    return days;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'expired':
        return 'bg-red-100 text-red-800';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800';
      case 'pending_renewal':
        return 'bg-yellow-100 text-yellow-800';
      case 'lapsed':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getClaimStatusColor = (status: string) => {
    switch (status) {
      case 'submitted':
        return 'bg-blue-100 text-blue-800';
      case 'under_investigation':
        return 'bg-yellow-100 text-yellow-800';
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'settled':
        return 'bg-green-100 text-green-800';
      case 'closed':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const daysUntilRenewal = getDaysUntilRenewal(policy.renewalDate);

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
        {/* Header */}
        <div className="flex justify-between items-start mb-6">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">{policy.policyNumber}</h2>
            <p className="text-gray-600 mt-1">{policy.provider} - {policy.policyType.replace('_', ' ').toUpperCase()}</p>
          </div>
          <div className="flex items-center space-x-2">
            <Badge className={getStatusColor(policy.status)}>
              {policy.status.replace('_', ' ').charAt(0).toUpperCase() + policy.status.replace('_', ' ').slice(1)}
            </Badge>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="h-6 w-6" />
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Policy Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Building className="h-5 w-5 mr-2" />
                Policy Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <label className="text-sm font-medium text-gray-600">Policy Type</label>
                <p className="text-gray-900 capitalize">{policy.policyType.replace('_', ' ')}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">Coverage Type</label>
                <p className="text-gray-900 capitalize">{policy.coverageType.replace('_', ' ')}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">Effective Date</label>
                <p className="text-gray-900">{new Date(policy.effectiveDate).toLocaleDateString()}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">Expiration Date</label>
                <p className="text-gray-900">{new Date(policy.expirationDate).toLocaleDateString()}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">Auto Renewal</label>
                <p className="text-gray-900">{policy.autoRenewal ? 'Yes' : 'No'}</p>
              </div>
            </CardContent>
          </Card>

          {/* Financial Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <DollarSign className="h-5 w-5 mr-2" />
                Financial Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <label className="text-sm font-medium text-gray-600">Coverage Amount</label>
                <p className="text-gray-900 text-lg font-semibold">{formatCurrency(policy.coverageAmount)}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">Deductible</label>
                <p className="text-gray-900">{formatCurrency(policy.deductible)}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">Annual Premium</label>
                <p className="text-gray-900">{formatCurrency(policy.premium.annual)}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">Payment Frequency</label>
                <p className="text-gray-900 capitalize">{policy.premium.paymentFrequency}</p>
              </div>
              {policy.premium.monthly && (
                <div>
                  <label className="text-sm font-medium text-gray-600">Monthly Premium</label>
                  <p className="text-gray-900">{formatCurrency(policy.premium.monthly)}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Coverage Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Shield className="h-5 w-5 mr-2" />
                Coverage Details
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-3">
                <div className="flex items-center">
                  {policy.coverage.propertyDamage ? (
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  ) : (
                    <X className="h-4 w-4 text-red-500 mr-2" />
                  )}
                  <span className="text-sm">Property Damage</span>
                </div>
                <div className="flex items-center">
                  {policy.coverage.businessInterruption ? (
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  ) : (
                    <X className="h-4 w-4 text-red-500 mr-2" />
                  )}
                  <span className="text-sm">Business Interruption</span>
                </div>
                <div className="flex items-center">
                  {policy.coverage.equipmentBreakdown ? (
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  ) : (
                    <X className="h-4 w-4 text-red-500 mr-2" />
                  )}
                  <span className="text-sm">Equipment Breakdown</span>
                </div>
                <div className="flex items-center">
                  {policy.coverage.naturalDisasters ? (
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  ) : (
                    <X className="h-4 w-4 text-red-500 mr-2" />
                  )}
                  <span className="text-sm">Natural Disasters</span>
                </div>
                <div className="flex items-center">
                  {policy.coverage.theft ? (
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  ) : (
                    <X className="h-4 w-4 text-red-500 mr-2" />
                  )}
                  <span className="text-sm">Theft</span>
                </div>
                <div className="flex items-center">
                  {policy.coverage.liability ? (
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  ) : (
                    <X className="h-4 w-4 text-red-500 mr-2" />
                  )}
                  <span className="text-sm">Liability</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Renewal Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Clock className="h-5 w-5 mr-2" />
                Renewal Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <label className="text-sm font-medium text-gray-600">Renewal Date</label>
                <div className="flex items-center space-x-2">
                  <p className="text-gray-900">{new Date(policy.renewalDate).toLocaleDateString()}</p>
                  {policy.status === 'active' && (
                    <div className="flex items-center">
                      {daysUntilRenewal <= 60 ? (
                        <AlertTriangle className="h-4 w-4 text-yellow-500" />
                      ) : (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      )}
                      <span className="text-sm text-gray-500 ml-1">
                        {daysUntilRenewal > 0 ? `${daysUntilRenewal} days` : 'Overdue'}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Exclusions */}
        {policy.exclusions.length > 0 && (
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Policy Exclusions</CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="list-disc list-inside space-y-1">
                {policy.exclusions.map((exclusion, index) => (
                  <li key={index} className="text-sm text-gray-700">{exclusion}</li>
                ))}
              </ul>
            </CardContent>
          </Card>
        )}

        {/* Documents */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="h-5 w-5 mr-2" />
              Documents ({policy.documents.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {policy.documents.length > 0 ? (
              <div className="space-y-2">
                {policy.documents.map((doc) => (
                  <div key={doc.id} className="flex items-center justify-between p-2 border rounded">
                    <div>
                      <p className="text-sm font-medium text-gray-900">{doc.name}</p>
                      <p className="text-xs text-gray-500">
                        {doc.type.replace('_', ' ')} • {formatFileSize(doc.size)} • {new Date(doc.uploadDate).toLocaleDateString()}
                      </p>
                    </div>
                    <button className="text-blue-600 hover:text-blue-800">
                      <Download className="h-4 w-4" />
                    </button>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-sm">No documents uploaded</p>
            )}
          </CardContent>
        </Card>

        {/* Claims History */}
        {policy.claims.length > 0 && (
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Claims History ({policy.claims.length})</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {policy.claims.map((claim) => (
                  <div key={claim.id} className="border rounded-lg p-4">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h4 className="font-medium text-gray-900">Claim #{claim.claimNumber}</h4>
                        <p className="text-sm text-gray-600">{claim.description}</p>
                      </div>
                      <Badge className={getClaimStatusColor(claim.status)}>
                        {claim.status.replace('_', ' ')}
                      </Badge>
                    </div>
                    <div className="flex items-center text-sm text-gray-500 space-x-4">
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-1" />
                        Incident: {new Date(claim.incidentDate).toLocaleDateString()}
                      </div>
                      <div>
                        Estimated: {formatCurrency(claim.estimatedAmount)}
                      </div>
                      {claim.approvedAmount && (
                        <div>
                          Approved: {formatCurrency(claim.approvedAmount)}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Notes */}
        {policy.notes && (
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Notes</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700">{policy.notes}</p>
            </CardContent>
          </Card>
        )}

        {/* Action Buttons */}
        <div className="flex justify-end space-x-3 mt-6 pt-6 border-t">
          <button
            onClick={() => onFileClaim(policy)}
            className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700"
          >
            File Claim
          </button>
          <button
            onClick={() => onEditPolicy(policy)}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Edit Policy
          </button>
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default InsurancePolicyDetails;
