import React, { useState, useMemo } from 'react';
import { 
  TrendingDown, 
  Clock, 
  AlertTriangle, 
  Search, 
  Filter, 
  Download,
  BarChart3,
  LineChart,
  Package,
  Zap,
  Battery,
  Settings as SettingsIcon
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/Card';
import { Badge } from '../components/ui/Badge';
import { AssetDegradation, AssetType, DegradationSummary } from '../types';
import { mockAssetDegradation } from '../services/mockData';

/**
 * Degradation Overview Page Component
 * 
 * Provides comprehensive asset degradation tracking with:
 * - Asset degradation table with filtering and sorting
 * - Age distribution charts
 * - Expected degradation curves
 * - Risk assessment integration
 */
const DegradationOverview: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<AssetType | 'all'>('all');
  const [sortBy, setSortBy] = useState<'age' | 'degradation' | 'risk' | 'remaining'>('age');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // Calculate degradation summary
  const degradationSummary: DegradationSummary = useMemo(() => {
    const assets = mockAssetDegradation;
    const totalAssets = assets.length;
    const averageAge = assets.reduce((sum, asset) => sum + asset.currentAge, 0) / totalAssets;
    
    const oldestAsset = assets.reduce((oldest, asset) => 
      asset.currentAge > oldest.currentAge ? asset : oldest
    );

    const projectedPerformanceLoss = {
      nextYear: assets.reduce((sum, asset) => sum + asset.degradationRate.annual, 0) / totalAssets,
      next5Years: assets.reduce((sum, asset) => sum + (asset.degradationRate.annual * 5), 0) / totalAssets,
      endOfLife: assets.reduce((sum, asset) => sum + asset.degradationRate.projected, 0) / totalAssets
    };

    const assetsByAge = [
      { range: '0-5 years', count: assets.filter(a => a.currentAge <= 5).length, percentage: 0 },
      { range: '5-10 years', count: assets.filter(a => a.currentAge > 5 && a.currentAge <= 10).length, percentage: 0 },
      { range: '10-15 years', count: assets.filter(a => a.currentAge > 10 && a.currentAge <= 15).length, percentage: 0 },
      { range: '15+ years', count: assets.filter(a => a.currentAge > 15).length, percentage: 0 }
    ].map(bucket => ({
      ...bucket,
      percentage: (bucket.count / totalAssets) * 100
    }));

    const assetTypes: AssetType[] = ['solar_panel', 'inverter', 'battery', 'mounting_system', 'transformer'];
    const degradationByType = assetTypes.map(type => {
      const typeAssets = assets.filter(a => a.assetType === type);
      return {
        type,
        averageDegradation: typeAssets.length > 0 
          ? typeAssets.reduce((sum, a) => sum + a.degradationRate.cumulative, 0) / typeAssets.length 
          : 0,
        count: typeAssets.length
      };
    }).filter(item => item.count > 0);

    const riskDistribution = {
      low: assets.filter(a => a.riskFactors.overall <= 3).length,
      medium: assets.filter(a => a.riskFactors.overall > 3 && a.riskFactors.overall <= 6).length,
      high: assets.filter(a => a.riskFactors.overall > 6 && a.riskFactors.overall <= 8).length,
      critical: assets.filter(a => a.riskFactors.overall > 8).length
    };

    return {
      totalAssets,
      averageAge,
      oldestAsset: {
        id: oldestAsset.id,
        name: oldestAsset.assetName,
        age: oldestAsset.currentAge,
        type: oldestAsset.assetType
      },
      projectedPerformanceLoss,
      assetsByAge,
      degradationByType,
      riskDistribution
    };
  }, []);

  // Filter and sort assets
  const filteredAssets = useMemo(() => {
    let filtered = mockAssetDegradation.filter(asset => {
      const matchesSearch = asset.assetName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           asset.manufacturer.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           asset.model.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesType = typeFilter === 'all' || asset.assetType === typeFilter;
      
      return matchesSearch && matchesType;
    });

    // Sort assets
    filtered.sort((a, b) => {
      let aValue: number, bValue: number;
      
      switch (sortBy) {
        case 'age':
          aValue = a.currentAge;
          bValue = b.currentAge;
          break;
        case 'degradation':
          aValue = a.degradationRate.cumulative;
          bValue = b.degradationRate.cumulative;
          break;
        case 'risk':
          aValue = a.riskFactors.overall;
          bValue = b.riskFactors.overall;
          break;
        case 'remaining':
          aValue = a.remainingLife;
          bValue = b.remainingLife;
          break;
        default:
          aValue = a.currentAge;
          bValue = b.currentAge;
      }

      return sortOrder === 'asc' ? aValue - bValue : bValue - aValue;
    });

    return filtered;
  }, [searchTerm, typeFilter, sortBy, sortOrder]);

  const getAssetIcon = (type: AssetType) => {
    switch (type) {
      case 'solar_panel':
        return <Package className="h-4 w-4" />;
      case 'inverter':
        return <Zap className="h-4 w-4" />;
      case 'battery':
        return <Battery className="h-4 w-4" />;
      case 'mounting_system':
      case 'transformer':
      case 'electrical_components':
      case 'monitoring_system':
      case 'combiner_box':
        return <SettingsIcon className="h-4 w-4" />;
      default:
        return <Package className="h-4 w-4" />;
    }
  };

  const getRiskColor = (risk: number) => {
    if (risk <= 3) return 'bg-green-100 text-green-800';
    if (risk <= 6) return 'bg-yellow-100 text-yellow-800';
    if (risk <= 8) return 'bg-orange-100 text-orange-800';
    return 'bg-red-100 text-red-800';
  };

  const getRiskLabel = (risk: number) => {
    if (risk <= 3) return 'Low';
    if (risk <= 6) return 'Medium';
    if (risk <= 8) return 'High';
    return 'Critical';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Asset Degradation Overview</h1>
          <p className="text-gray-600 mt-1">
            Monitor asset aging, degradation rates, and remaining useful life
          </p>
        </div>
        <div className="flex space-x-2">
          <button className="flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50">
            <BarChart3 className="h-4 w-4 mr-2" />
            View Charts
          </button>
          <button className="flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50">
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </button>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Average Site Age</p>
                <p className="text-2xl font-bold text-gray-900">{degradationSummary.averageAge.toFixed(1)} years</p>
              </div>
              <Clock className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Oldest Asset</p>
                <p className="text-lg font-bold text-gray-900">{degradationSummary.oldestAsset.name}</p>
                <p className="text-sm text-gray-500">{degradationSummary.oldestAsset.age.toFixed(1)} years old</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Projected Loss (5yr)</p>
                <p className="text-2xl font-bold text-red-600">{degradationSummary.projectedPerformanceLoss.next5Years.toFixed(1)}%</p>
              </div>
              <TrendingDown className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">High Risk Assets</p>
                <p className="text-2xl font-bold text-orange-600">
                  {degradationSummary.riskDistribution.high + degradationSummary.riskDistribution.critical}
                </p>
              </div>
              <AlertTriangle className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Age Distribution Chart */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <BarChart3 className="h-5 w-5 mr-2" />
            Asset Age Distribution
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {degradationSummary.assetsByAge.map((bucket, index) => (
              <div key={index} className="flex items-center">
                <div className="w-24 text-sm font-medium text-gray-600">{bucket.range}</div>
                <div className="flex-1 mx-4">
                  <div className="w-full bg-gray-200 rounded-full h-6">
                    <div 
                      className="bg-blue-600 h-6 rounded-full flex items-center justify-center text-white text-xs font-medium"
                      style={{ width: `${Math.max(bucket.percentage, 5)}%` }}
                    >
                      {bucket.count > 0 && `${bucket.count}`}
                    </div>
                  </div>
                </div>
                <div className="w-16 text-sm text-gray-600 text-right">
                  {bucket.percentage.toFixed(1)}%
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center space-x-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search assets..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <select 
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value as AssetType | 'all')}
              className="border border-gray-300 rounded-lg px-3 py-2"
            >
              <option value="all">All Types</option>
              <option value="solar_panel">Solar Panels</option>
              <option value="inverter">Inverters</option>
              <option value="battery">Batteries</option>
              <option value="mounting_system">Mounting Systems</option>
              <option value="transformer">Transformers</option>
            </select>
            <select 
              value={`${sortBy}-${sortOrder}`}
              onChange={(e) => {
                const [sort, order] = e.target.value.split('-');
                setSortBy(sort as any);
                setSortOrder(order as 'asc' | 'desc');
              }}
              className="border border-gray-300 rounded-lg px-3 py-2"
            >
              <option value="age-desc">Age (Oldest First)</option>
              <option value="age-asc">Age (Newest First)</option>
              <option value="degradation-desc">Degradation (Highest First)</option>
              <option value="degradation-asc">Degradation (Lowest First)</option>
              <option value="risk-desc">Risk (Highest First)</option>
              <option value="risk-asc">Risk (Lowest First)</option>
              <option value="remaining-asc">Remaining Life (Shortest First)</option>
              <option value="remaining-desc">Remaining Life (Longest First)</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Assets Table */}
      <Card>
        <CardHeader>
          <CardTitle>Asset Degradation Details ({filteredAssets.length} assets)</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-medium text-gray-600">Asset</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-600">Type</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-600">Age</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-600">Degradation</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-600">Remaining Life</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-600">Risk Level</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-600">Performance</th>
                </tr>
              </thead>
              <tbody>
                {filteredAssets.map((asset) => (
                  <tr key={asset.id} className="border-b border-gray-100 hover:bg-gray-50">
                    <td className="py-4 px-4">
                      <div className="flex items-center">
                        {getAssetIcon(asset.assetType)}
                        <div className="ml-3">
                          <div className="font-medium text-gray-900">{asset.assetName}</div>
                          <div className="text-sm text-gray-500">{asset.manufacturer} {asset.model}</div>
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <Badge className="bg-blue-100 text-blue-800 capitalize">
                        {asset.assetType.replace('_', ' ')}
                      </Badge>
                    </td>
                    <td className="py-4 px-4">
                      <div>
                        <div className="font-medium text-gray-900">{asset.currentAge.toFixed(1)} years</div>
                        <div className="text-sm text-gray-500">
                          Installed: {new Date(asset.installationDate).toLocaleDateString()}
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <div>
                        <div className="font-medium text-gray-900">{asset.degradationRate.cumulative.toFixed(1)}%</div>
                        <div className="text-sm text-gray-500">
                          {asset.degradationRate.annual.toFixed(2)}% annually
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <div>
                        <div className="font-medium text-gray-900">{asset.remainingLife.toFixed(1)} years</div>
                        <div className="text-sm text-gray-500">
                          of {asset.expectedLifespan} total
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <Badge className={getRiskColor(asset.riskFactors.overall)}>
                        {getRiskLabel(asset.riskFactors.overall)} ({asset.riskFactors.overall.toFixed(1)})
                      </Badge>
                    </td>
                    <td className="py-4 px-4">
                      <div>
                        <div className="font-medium text-gray-900">
                          {asset.performanceMetrics.currentRating.toFixed(0)} 
                          {asset.assetType === 'solar_panel' ? 'W' : asset.assetType === 'battery' ? 'kWh' : 'kW'}
                        </div>
                        <div className="text-sm text-red-500">
                          -{asset.performanceMetrics.degradationLoss.toFixed(1)} 
                          {asset.assetType === 'solar_panel' ? 'W' : asset.assetType === 'battery' ? 'kWh' : 'kW'} lost
                        </div>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DegradationOverview;
