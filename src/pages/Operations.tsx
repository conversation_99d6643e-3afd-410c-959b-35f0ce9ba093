import React, { useState } from 'react';
import {
  Settings,
  Search,
  AlertTriangle,
  CheckCircle,
  Clock,
  Mail,
  MessageSquare,
  FileText,
  Download,
  Upload,
  ExternalLink,
  Zap,
  Activity,
  Filter,
  Plus,
  Shield,
  Building
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/Card';
import { Badge } from '../components/ui/Badge';
import WarrantyTracker from '../components/Operations/WarrantyTracker';
import InsuranceTracker from '../components/Operations/InsuranceTracker';
import WarrantyDetails from '../components/Operations/WarrantyDetails';
import InsurancePolicyDetails from '../components/Operations/InsurancePolicyDetails';
import { mockWarranties, mockInsurancePolicies } from '../services/mockData';
import { WarrantyRecord, InsurancePolicy } from '../types';

/**
 * Operations Page Component
 * 
 * Post-construction operations and maintenance interface providing:
 * - Defect detection and management
 * - Performance monitoring
 * - Maintenance scheduling
 * - Integration with external tools (Gmail, Slack, Google Sheets)
 * - Export to defect management systems
 */
const Operations: React.FC = () => {
  const [activeTab, setActiveTab] = useState('defects');

  // Mock data for warranties and insurance
  const [warranties] = useState<WarrantyRecord[]>(mockWarranties);
  const [insurancePolicies] = useState<InsurancePolicy[]>(mockInsurancePolicies);

  // Modal state management
  const [selectedWarranty, setSelectedWarranty] = useState<WarrantyRecord | null>(null);
  const [selectedPolicy, setSelectedPolicy] = useState<InsurancePolicy | null>(null);
  const [showWarrantyDetails, setShowWarrantyDetails] = useState(false);
  const [showPolicyDetails, setShowPolicyDetails] = useState(false);

  // Handlers for warranty management
  const handleAddWarranty = () => {
    console.log('Add warranty clicked');
    // TODO: Implement add warranty modal
  };

  const handleViewWarranty = (warranty: WarrantyRecord) => {
    setSelectedWarranty(warranty);
    setShowWarrantyDetails(true);
  };

  const handleEditWarranty = (warranty: WarrantyRecord) => {
    console.log('Edit warranty:', warranty);
    // TODO: Implement edit warranty modal
  };

  const handleFileWarrantyClaim = (warranty: WarrantyRecord) => {
    console.log('File warranty claim:', warranty);
    // TODO: Implement file warranty claim modal
  };

  // Handlers for insurance management
  const handleAddPolicy = () => {
    console.log('Add policy clicked');
    // TODO: Implement add policy modal
  };

  const handleViewPolicy = (policy: InsurancePolicy) => {
    setSelectedPolicy(policy);
    setShowPolicyDetails(true);
  };

  const handleEditPolicy = (policy: InsurancePolicy) => {
    console.log('Edit policy:', policy);
    // TODO: Implement edit policy modal
  };

  const handleFileClaim = (policy: InsurancePolicy) => {
    console.log('File claim for policy:', policy);
    // TODO: Implement file claim modal
  };

  // Helper function to check warranty status for a defect
  const getWarrantyStatusForDefect = (defectId: string) => {
    // Find warranty claims that reference this defect
    const warrantyWithClaim = warranties.find(w =>
      w.claims.some(c => c.defectId === defectId)
    );

    if (warrantyWithClaim) {
      const claim = warrantyWithClaim.claims.find(c => c.defectId === defectId);
      return {
        hasWarranty: true,
        status: claim?.status || 'unknown',
        warrantyId: warrantyWithClaim.id
      };
    }

    // Check if there's an active warranty for the component (simplified logic)
    const activeWarranty = warranties.find(w =>
      w.status === 'active' &&
      (defectId.includes('A-15-23') && w.componentName.includes('A-15-23') ||
       defectId.includes('C-22-05') && w.componentName.includes('C-22-05'))
    );

    return {
      hasWarranty: !!activeWarranty,
      status: activeWarranty ? 'covered' : 'not_covered',
      warrantyId: activeWarranty?.id
    };
  };

  const defects = [
    {
      id: 'DEF-001',
      title: 'Hotspot detected in Panel A-15-23',
      severity: 'High',
      status: 'Open',
      assignee: 'John Smith',
      created: '2025-01-15',
      lastUpdate: '2025-01-16',
      location: 'Block A, Row 15',
      powerLoss: '2.5 kW',
      estimatedCost: '$1,250'
    },
    {
      id: 'DEF-002',
      title: 'Soiling on Panel Array B-08',
      severity: 'Medium',
      status: 'In Progress',
      assignee: 'Sarah Johnson',
      created: '2025-01-14',
      lastUpdate: '2025-01-16',
      location: 'Block B, Row 8',
      powerLoss: '0.8 kW',
      estimatedCost: '$150'
    },
    {
      id: 'DEF-003',
      title: 'Cracked panel C-22-05',
      severity: 'Critical',
      status: 'Resolved',
      assignee: 'Mike Davis',
      created: '2025-01-10',
      lastUpdate: '2025-01-15',
      location: 'Block C, Row 22',
      powerLoss: '4.2 kW',
      estimatedCost: '$2,800'
    }
  ];

  const integrations = [
    {
      name: 'Gmail',
      icon: <Mail className="h-6 w-6" />,
      status: 'Connected',
      description: 'Sync defect notifications and status updates',
      lastSync: '2 minutes ago',
      color: 'green'
    },
    {
      name: 'Slack',
      icon: <MessageSquare className="h-6 w-6" />,
      status: 'Connected',
      description: 'Real-time alerts and team collaboration',
      lastSync: '5 minutes ago',
      color: 'green'
    },
    {
      name: 'Google Sheets',
      icon: <FileText className="h-6 w-6" />,
      status: 'Connected',
      description: 'Export defect data and maintenance logs',
      lastSync: '1 hour ago',
      color: 'green'
    },
    {
      name: 'Jira',
      icon: <ExternalLink className="h-6 w-6" />,
      status: 'Not Connected',
      description: 'Advanced defect tracking and workflow management',
      lastSync: 'Never',
      color: 'gray'
    }
  ];

  const getSeverityColor = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'critical':
        return 'bg-red-100 text-red-800';
      case 'high':
        return 'bg-orange-100 text-orange-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'resolved':
        return 'bg-green-100 text-green-800';
      case 'in progress':
        return 'bg-blue-100 text-blue-800';
      case 'open':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Operations & Maintenance</h1>
          <p className="text-gray-600 mt-1">
            Monitor performance, manage defects, and maintain system health
          </p>
        </div>
        <div className="flex space-x-2">
          <button className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
            <Plus className="h-4 w-4 mr-2" />
            Report Issue
          </button>
          <button className="flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50">
            <Download className="h-4 w-4 mr-2" />
            Export Data
          </button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Issues</p>
                <p className="text-2xl font-bold text-gray-900">12</p>
              </div>
              <div className="h-12 w-12 bg-red-50 rounded-lg flex items-center justify-center">
                <AlertTriangle className="h-6 w-6 text-red-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center">
              <span className="text-sm font-medium text-red-600">3 Critical</span>
              <span className="text-sm text-gray-500 ml-2">require attention</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">System Health</p>
                <p className="text-2xl font-bold text-gray-900">94.2%</p>
              </div>
              <div className="h-12 w-12 bg-green-50 rounded-lg flex items-center justify-center">
                <Activity className="h-6 w-6 text-green-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center">
              <span className="text-sm font-medium text-green-600">Excellent</span>
              <span className="text-sm text-gray-500 ml-2">performance</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Power Output</p>
                <p className="text-2xl font-bold text-gray-900">1.18 MW</p>
              </div>
              <div className="h-12 w-12 bg-blue-50 rounded-lg flex items-center justify-center">
                <Zap className="h-6 w-6 text-blue-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center">
              <span className="text-sm font-medium text-blue-600">98.3%</span>
              <span className="text-sm text-gray-500 ml-2">of capacity</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Resolved Issues</p>
                <p className="text-2xl font-bold text-gray-900">47</p>
              </div>
              <div className="h-12 w-12 bg-green-50 rounded-lg flex items-center justify-center">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center">
              <span className="text-sm font-medium text-green-600">This month</span>
              <span className="text-sm text-gray-500 ml-2">avg 2.1 days</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('defects')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'defects'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Defect Management
          </button>
          <button
            onClick={() => setActiveTab('warranty')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'warranty'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Shield className="h-4 w-4 inline mr-1" />
            Warranty Tracker
          </button>
          <button
            onClick={() => setActiveTab('insurance')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'insurance'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Building className="h-4 w-4 inline mr-1" />
            Insurance
          </button>


        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'defects' && (
        <div className="space-y-6">
          {/* Search and Filters */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-4">
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search defects..."
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <select className="border border-gray-300 rounded-lg px-3 py-2">
                  <option>All Severities</option>
                  <option>Critical</option>
                  <option>High</option>
                  <option>Medium</option>
                  <option>Low</option>
                </select>
                <select className="border border-gray-300 rounded-lg px-3 py-2">
                  <option>All Status</option>
                  <option>Open</option>
                  <option>In Progress</option>
                  <option>Resolved</option>
                </select>
                <button className="flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50">
                  <Filter className="h-4 w-4 mr-2" />
                  More Filters
                </button>
              </div>
            </CardContent>
          </Card>

          {/* Defects Table */}
          <Card>
            <CardHeader>
              <CardTitle>Active Defects</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-3 px-4 font-medium text-gray-600">ID</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-600">Issue</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-600">Severity</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-600">Status</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-600">Assignee</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-600">Impact</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-600">Cost</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-600">Warranty</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-600">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {defects.map((defect) => (
                      <tr key={defect.id} className="border-b border-gray-100 hover:bg-gray-50">
                        <td className="py-4 px-4 font-mono text-sm">{defect.id}</td>
                        <td className="py-4 px-4">
                          <div>
                            <div className="font-medium text-gray-900">{defect.title}</div>
                            <div className="text-sm text-gray-500">{defect.location}</div>
                          </div>
                        </td>
                        <td className="py-4 px-4">
                          <Badge className={getSeverityColor(defect.severity)}>
                            {defect.severity}
                          </Badge>
                        </td>
                        <td className="py-4 px-4">
                          <Badge className={getStatusColor(defect.status)}>
                            {defect.status}
                          </Badge>
                        </td>
                        <td className="py-4 px-4 text-gray-600">{defect.assignee}</td>
                        <td className="py-4 px-4 text-gray-600">{defect.powerLoss}</td>
                        <td className="py-4 px-4 text-gray-600">{defect.estimatedCost}</td>
                        <td className="py-4 px-4">
                          {(() => {
                            const warrantyStatus = getWarrantyStatusForDefect(defect.id);
                            if (warrantyStatus.hasWarranty) {
                              if (warrantyStatus.status === 'submitted') {
                                return <Badge className="bg-blue-100 text-blue-800">Claim Submitted</Badge>;
                              } else if (warrantyStatus.status === 'covered') {
                                return <Badge className="bg-green-100 text-green-800">Under Warranty</Badge>;
                              } else {
                                return <Badge className="bg-yellow-100 text-yellow-800">Warranty Available</Badge>;
                              }
                            } else {
                              return <Badge className="bg-gray-100 text-gray-800">No Warranty</Badge>;
                            }
                          })()}
                        </td>
                        <td className="py-4 px-4">
                          <button className="text-blue-600 hover:text-blue-800 text-sm">
                            View Details
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {activeTab === 'warranty' && (
        <WarrantyTracker
          warranties={warranties}
          onAddWarranty={handleAddWarranty}
          onViewWarranty={handleViewWarranty}
          onEditWarranty={handleEditWarranty}
        />
      )}

      {activeTab === 'insurance' && (
        <InsuranceTracker
          policies={insurancePolicies}
          onAddPolicy={handleAddPolicy}
          onViewPolicy={handleViewPolicy}
          onEditPolicy={handleEditPolicy}
          onFileClaim={handleFileClaim}
        />
      )}





      {/* Warranty Details Modal */}
      {showWarrantyDetails && selectedWarranty && (
        <WarrantyDetails
          warranty={selectedWarranty}
          onClose={() => {
            setShowWarrantyDetails(false);
            setSelectedWarranty(null);
          }}
          onEditWarranty={handleEditWarranty}
          onFileClaim={handleFileWarrantyClaim}
        />
      )}

      {/* Insurance Policy Details Modal */}
      {showPolicyDetails && selectedPolicy && (
        <InsurancePolicyDetails
          policy={selectedPolicy}
          onClose={() => {
            setShowPolicyDetails(false);
            setSelectedPolicy(null);
          }}
          onEditPolicy={handleEditPolicy}
          onFileClaim={handleFileClaim}
        />
      )}
    </div>
  );
};

export default Operations;
