import React, { useState } from 'react';
import {
  Save,
  User,
  Bell,
  Shield,
  Database,
  Palette,
  Globe,
  Mail,
  MessageSquare,
  FileText,
  ExternalLink,
  Upload,
  Settings as SettingsIcon
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/Card';
import { Badge } from '../components/ui/Badge';

/**
 * Settings Page Component
 * 
 * Application settings and configuration:
 * - User profile settings
 * - Notification preferences
 * - Security settings
 * - Data management
 * - Appearance preferences
 * - System configuration
 */
const Settings: React.FC = () => {
  const [activeTab, setActiveTab] = useState('profile');
  const [settings, setSettings] = useState({
    profile: {
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '******-0123',
      role: 'Project Manager',
      timezone: 'America/Los_Angeles'
    },
    notifications: {
      emailAlerts: true,
      pushNotifications: true,
      criticalDefects: true,
      projectUpdates: true,
      weeklyReports: false
    },
    security: {
      twoFactorAuth: false,
      sessionTimeout: 30,
      passwordExpiry: 90
    },
    appearance: {
      theme: 'light',
      language: 'en',
      dateFormat: 'MM/DD/YYYY',
      currency: 'USD'
    }
  });

  const tabs = [
    { id: 'profile', label: 'Profile', icon: User },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'security', label: 'Security', icon: Shield },
    { id: 'integrations', label: 'Integrations', icon: ExternalLink },
    { id: 'data', label: 'Data', icon: Database },
    { id: 'appearance', label: 'Appearance', icon: Palette },
    { id: 'system', label: 'System', icon: Globe }
  ];

  // Integration data
  const integrations = [
    {
      name: 'Gmail',
      description: 'Send defect reports and notifications via email',
      status: 'Connected',
      lastSync: '2 hours ago',
      icon: <Mail className="h-6 w-6 text-red-600" />,
      color: 'red'
    },
    {
      name: 'Slack',
      description: 'Real-time team notifications and alerts',
      status: 'Connected',
      lastSync: '5 minutes ago',
      icon: <MessageSquare className="h-6 w-6 text-purple-600" />,
      color: 'purple'
    },
    {
      name: 'Google Sheets',
      description: 'Export data for analysis and reporting',
      status: 'Not Connected',
      lastSync: 'Never',
      icon: <FileText className="h-6 w-6 text-green-600" />,
      color: 'green'
    },
    {
      name: 'Microsoft Teams',
      description: 'Collaborate with team members',
      status: 'Not Connected',
      lastSync: 'Never',
      icon: <MessageSquare className="h-6 w-6 text-blue-600" />,
      color: 'blue'
    }
  ];

  const handleSettingChange = (category: string, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category as keyof typeof prev],
        [key]: value
      }
    }));
  };

  const handleSave = () => {
    // In a real app, this would save settings to the backend
    console.log('Saving settings:', settings);
    // Show success message
  };

  const renderProfileSettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="label">Full Name</label>
          <input
            type="text"
            value={settings.profile.name}
            onChange={(e) => handleSettingChange('profile', 'name', e.target.value)}
            className="input"
          />
        </div>
        <div>
          <label className="label">Email Address</label>
          <input
            type="email"
            value={settings.profile.email}
            onChange={(e) => handleSettingChange('profile', 'email', e.target.value)}
            className="input"
          />
        </div>
        <div>
          <label className="label">Phone Number</label>
          <input
            type="tel"
            value={settings.profile.phone}
            onChange={(e) => handleSettingChange('profile', 'phone', e.target.value)}
            className="input"
          />
        </div>
        <div>
          <label className="label">Role</label>
          <select
            value={settings.profile.role}
            onChange={(e) => handleSettingChange('profile', 'role', e.target.value)}
            className="select"
          >
            <option value="Project Manager">Project Manager</option>
            <option value="Technician">Technician</option>
            <option value="Analyst">Analyst</option>
            <option value="Administrator">Administrator</option>
          </select>
        </div>
        <div className="md:col-span-2">
          <label className="label">Timezone</label>
          <select
            value={settings.profile.timezone}
            onChange={(e) => handleSettingChange('profile', 'timezone', e.target.value)}
            className="select"
          >
            <option value="America/Los_Angeles">Pacific Time (PT)</option>
            <option value="America/Denver">Mountain Time (MT)</option>
            <option value="America/Chicago">Central Time (CT)</option>
            <option value="America/New_York">Eastern Time (ET)</option>
          </select>
        </div>
      </div>
    </div>
  );

  const renderNotificationSettings = () => (
    <div className="space-y-6">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="text-sm font-medium text-gray-900">Email Alerts</h4>
            <p className="text-sm text-gray-500">Receive notifications via email</p>
          </div>
          <input
            type="checkbox"
            checked={settings.notifications.emailAlerts}
            onChange={(e) => handleSettingChange('notifications', 'emailAlerts', e.target.checked)}
            className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
          />
        </div>
        
        <div className="flex items-center justify-between">
          <div>
            <h4 className="text-sm font-medium text-gray-900">Push Notifications</h4>
            <p className="text-sm text-gray-500">Receive browser notifications</p>
          </div>
          <input
            type="checkbox"
            checked={settings.notifications.pushNotifications}
            onChange={(e) => handleSettingChange('notifications', 'pushNotifications', e.target.checked)}
            className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
          />
        </div>
        
        <div className="flex items-center justify-between">
          <div>
            <h4 className="text-sm font-medium text-gray-900">Critical Defects</h4>
            <p className="text-sm text-gray-500">Immediate alerts for critical defects</p>
          </div>
          <input
            type="checkbox"
            checked={settings.notifications.criticalDefects}
            onChange={(e) => handleSettingChange('notifications', 'criticalDefects', e.target.checked)}
            className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
          />
        </div>
        
        <div className="flex items-center justify-between">
          <div>
            <h4 className="text-sm font-medium text-gray-900">Project Updates</h4>
            <p className="text-sm text-gray-500">Notifications about project progress</p>
          </div>
          <input
            type="checkbox"
            checked={settings.notifications.projectUpdates}
            onChange={(e) => handleSettingChange('notifications', 'projectUpdates', e.target.checked)}
            className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
          />
        </div>
        
        <div className="flex items-center justify-between">
          <div>
            <h4 className="text-sm font-medium text-gray-900">Weekly Reports</h4>
            <p className="text-sm text-gray-500">Weekly summary reports</p>
          </div>
          <input
            type="checkbox"
            checked={settings.notifications.weeklyReports}
            onChange={(e) => handleSettingChange('notifications', 'weeklyReports', e.target.checked)}
            className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
          />
        </div>
      </div>
    </div>
  );

  const renderSecuritySettings = () => (
    <div className="space-y-6">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="text-sm font-medium text-gray-900">Two-Factor Authentication</h4>
            <p className="text-sm text-gray-500">Add an extra layer of security</p>
          </div>
          <input
            type="checkbox"
            checked={settings.security.twoFactorAuth}
            onChange={(e) => handleSettingChange('security', 'twoFactorAuth', e.target.checked)}
            className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
          />
        </div>

        <div>
          <label className="label">Session Timeout (minutes)</label>
          <select
            value={settings.security.sessionTimeout}
            onChange={(e) => handleSettingChange('security', 'sessionTimeout', parseInt(e.target.value))}
            className="select"
          >
            <option value={15}>15 minutes</option>
            <option value={30}>30 minutes</option>
            <option value={60}>1 hour</option>
            <option value={120}>2 hours</option>
          </select>
        </div>

        <div>
          <label className="label">Password Expiry (days)</label>
          <select
            value={settings.security.passwordExpiry}
            onChange={(e) => handleSettingChange('security', 'passwordExpiry', parseInt(e.target.value))}
            className="select"
          >
            <option value={30}>30 days</option>
            <option value={60}>60 days</option>
            <option value={90}>90 days</option>
            <option value={180}>180 days</option>
            <option value={0}>Never</option>
          </select>
        </div>
      </div>
    </div>
  );

  const renderIntegrationsSettings = () => (
    <div className="space-y-6">
      {/* Integration Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {integrations.map((integration, index) => (
          <Card key={index}>
            <CardContent className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex items-center">
                  <div className={`h-12 w-12 bg-${integration.color}-50 rounded-lg flex items-center justify-center mr-4`}>
                    {integration.icon}
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">{integration.name}</h3>
                    <p className="text-sm text-gray-600 mt-1">{integration.description}</p>
                  </div>
                </div>
                <Badge
                  className={integration.status === 'Connected' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}
                >
                  {integration.status}
                </Badge>
              </div>

              <div className="mt-4 flex items-center justify-between">
                <span className="text-sm text-gray-500">
                  Last sync: {integration.lastSync}
                </span>
                <div className="flex space-x-2">
                  {integration.status === 'Connected' ? (
                    <>
                      <button className="text-blue-600 hover:text-blue-800 text-sm">
                        Configure
                      </button>
                      <button className="text-red-600 hover:text-red-800 text-sm">
                        Disconnect
                      </button>
                    </>
                  ) : (
                    <button className="text-blue-600 hover:text-blue-800 text-sm">
                      Connect
                    </button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Integration Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Data Export & Sync</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button className="flex items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-500 hover:bg-blue-50">
              <div className="text-center">
                <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p className="font-medium text-gray-900">Export to Gmail</p>
                <p className="text-sm text-gray-500">Send defect reports via email</p>
              </div>
            </button>

            <button className="flex items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-500 hover:bg-blue-50">
              <div className="text-center">
                <MessageSquare className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p className="font-medium text-gray-900">Sync to Slack</p>
                <p className="text-sm text-gray-500">Real-time team notifications</p>
              </div>
            </button>

            <button className="flex items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-500 hover:bg-blue-50">
              <div className="text-center">
                <FileText className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p className="font-medium text-gray-900">Export to Sheets</p>
                <p className="text-sm text-gray-500">Bulk data export and analysis</p>
              </div>
            </button>
          </div>
        </CardContent>
      </Card>

      {/* Automation Rules */}
      <Card>
        <CardHeader>
          <CardTitle>Automation Rules</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h4 className="font-medium text-gray-900">Critical Defect Alert</h4>
                <p className="text-sm text-gray-600">Automatically notify team via Slack when critical defects are detected</p>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-green-600">Active</span>
                <button className="text-blue-600 hover:text-blue-800 text-sm">Edit</button>
              </div>
            </div>

            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h4 className="font-medium text-gray-900">Weekly Status Report</h4>
                <p className="text-sm text-gray-600">Send weekly defect summary to stakeholders via Gmail</p>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-green-600">Active</span>
                <button className="text-blue-600 hover:text-blue-800 text-sm">Edit</button>
              </div>
            </div>

            <button className="w-full p-4 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-blue-500 hover:text-blue-600">
              + Add New Automation Rule
            </button>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderContent = () => {
    switch (activeTab) {
      case 'profile':
        return renderProfileSettings();
      case 'notifications':
        return renderNotificationSettings();
      case 'security':
        return renderSecuritySettings();
      case 'integrations':
        return renderIntegrationsSettings();
      case 'data':
        return <div className="text-center py-8 text-gray-500">Data management settings coming soon...</div>;
      case 'appearance':
        return <div className="text-center py-8 text-gray-500">Appearance settings coming soon...</div>;
      case 'system':
        return <div className="text-center py-8 text-gray-500">System settings coming soon...</div>;
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
          <p className="text-gray-500 mt-1">Manage your account and application preferences</p>
        </div>
        <button
          onClick={handleSave}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          <Save className="h-4 w-4 mr-2" />
          Save Changes
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Sidebar */}
        <div className="lg:col-span-1">
          <Card>
            <CardContent className="p-4">
              <nav className="space-y-1">
                {tabs.map((tab) => {
                  const Icon = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                        activeTab === tab.id
                          ? 'bg-blue-100 text-blue-700'
                          : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                      }`}
                    >
                      <Icon className="h-4 w-4 mr-3" />
                      {tab.label}
                    </button>
                  );
                })}
              </nav>
            </CardContent>
          </Card>
        </div>

        {/* Content */}
        <div className="lg:col-span-3">
          <Card>
            <CardHeader>
              <CardTitle>{tabs.find(t => t.id === activeTab)?.label}</CardTitle>
            </CardHeader>
            <CardContent>
              {renderContent()}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Settings;
