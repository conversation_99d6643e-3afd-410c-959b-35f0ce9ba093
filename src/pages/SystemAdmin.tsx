import React, { useState } from 'react';
import { 
  Settings as SettingsIcon, 
  Database, 
  Shield, 
  Users, 
  Activity, 
  Server,
  HardDrive,
  Wifi,
  AlertTriangle,
  CheckCircle,
  Clock,
  Download,
  Upload,
  RefreshCw,
  Monitor,
  Zap
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/Card';
import { Badge } from '../components/ui/Badge';

/**
 * System Administration Page Component
 * 
 * Provides system-wide configuration and administration:
 * - System health monitoring
 * - User management
 * - Database administration
 * - Security settings
 * - Maintenance scheduling
 * - System integrations
 * - Performance monitoring
 */
const SystemAdmin: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');

  // System health data
  const systemHealth = {
    status: 'healthy',
    uptime: '99.8%',
    lastBackup: '2 hours ago',
    activeUsers: 24,
    totalStorage: '2.4 TB',
    usedStorage: '1.2 TB',
    cpuUsage: 45,
    memoryUsage: 62,
    networkLatency: '12ms'
  };

  const systemServices = [
    {
      name: 'Database Service',
      status: 'running',
      uptime: '15 days',
      cpu: 25,
      memory: 45,
      icon: <Database className="h-5 w-5" />
    },
    {
      name: 'Authentication Service',
      status: 'running',
      uptime: '15 days',
      cpu: 5,
      memory: 12,
      icon: <Shield className="h-5 w-5" />
    },
    {
      name: 'File Storage Service',
      status: 'running',
      uptime: '15 days',
      cpu: 15,
      memory: 28,
      icon: <HardDrive className="h-5 w-5" />
    },
    {
      name: 'Integration Service',
      status: 'warning',
      uptime: '2 days',
      cpu: 35,
      memory: 55,
      icon: <Wifi className="h-5 w-5" />
    }
  ];

  const maintenanceTasks = [
    {
      id: 1,
      task: 'Database Optimization',
      schedule: 'Weekly - Sundays 2:00 AM',
      lastRun: '2024-01-14',
      nextRun: '2024-01-21',
      status: 'scheduled',
      duration: '45 minutes'
    },
    {
      id: 2,
      task: 'System Backup',
      schedule: 'Daily - 1:00 AM',
      lastRun: '2024-01-16',
      nextRun: '2024-01-17',
      status: 'scheduled',
      duration: '2 hours'
    },
    {
      id: 3,
      task: 'Log Cleanup',
      schedule: 'Monthly - 1st Sunday',
      lastRun: '2024-01-07',
      nextRun: '2024-02-04',
      status: 'scheduled',
      duration: '30 minutes'
    },
    {
      id: 4,
      task: 'Security Scan',
      schedule: 'Weekly - Wednesdays 3:00 AM',
      lastRun: '2024-01-10',
      nextRun: '2024-01-17',
      status: 'overdue',
      duration: '1 hour'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running':
      case 'healthy':
      case 'scheduled':
        return 'bg-green-100 text-green-800';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800';
      case 'error':
      case 'overdue':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
      case 'healthy':
      case 'scheduled':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      case 'error':
      case 'overdue':
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const tabs = [
    { id: 'overview', label: 'System Overview', icon: Monitor },
    { id: 'services', label: 'Services', icon: Server },
    { id: 'users', label: 'User Management', icon: Users },
    { id: 'maintenance', label: 'Maintenance', icon: SettingsIcon },
    { id: 'security', label: 'Security', icon: Shield },
    { id: 'performance', label: 'Performance', icon: Activity }
  ];

  const renderOverview = () => (
    <div className="space-y-6">
      {/* System Health Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">System Status</p>
                <p className="text-2xl font-bold text-green-600">Healthy</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Uptime</p>
                <p className="text-2xl font-bold text-gray-900">{systemHealth.uptime}</p>
              </div>
              <Activity className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Users</p>
                <p className="text-2xl font-bold text-gray-900">{systemHealth.activeUsers}</p>
              </div>
              <Users className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Storage Used</p>
                <p className="text-2xl font-bold text-gray-900">50%</p>
                <p className="text-sm text-gray-500">{systemHealth.usedStorage} / {systemHealth.totalStorage}</p>
              </div>
              <HardDrive className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Resource Usage */}
      <Card>
        <CardHeader>
          <CardTitle>Resource Usage</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-gray-600">CPU Usage</span>
                <span className="text-sm text-gray-900">{systemHealth.cpuUsage}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full" 
                  style={{ width: `${systemHealth.cpuUsage}%` }}
                ></div>
              </div>
            </div>

            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-gray-600">Memory Usage</span>
                <span className="text-sm text-gray-900">{systemHealth.memoryUsage}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-green-600 h-2 rounded-full" 
                  style={{ width: `${systemHealth.memoryUsage}%` }}
                ></div>
              </div>
            </div>

            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-gray-600">Network Latency</span>
                <span className="text-sm text-gray-900">{systemHealth.networkLatency}</span>
              </div>
              <div className="flex items-center">
                <div className="w-full bg-gray-200 rounded-full h-2 mr-2">
                  <div className="bg-green-600 h-2 rounded-full" style={{ width: '85%' }}></div>
                </div>
                <span className="text-xs text-green-600">Good</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Recent Activities */}
      <Card>
        <CardHeader>
          <CardTitle>Recent System Activities</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
              <div className="flex items-center">
                <CheckCircle className="h-5 w-5 text-green-600 mr-3" />
                <div>
                  <p className="text-sm font-medium text-gray-900">System backup completed successfully</p>
                  <p className="text-xs text-gray-500">2 hours ago</p>
                </div>
              </div>
            </div>

            <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
              <div className="flex items-center">
                <RefreshCw className="h-5 w-5 text-blue-600 mr-3" />
                <div>
                  <p className="text-sm font-medium text-gray-900">Database optimization started</p>
                  <p className="text-xs text-gray-500">6 hours ago</p>
                </div>
              </div>
            </div>

            <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
              <div className="flex items-center">
                <AlertTriangle className="h-5 w-5 text-yellow-600 mr-3" />
                <div>
                  <p className="text-sm font-medium text-gray-900">Integration service restarted</p>
                  <p className="text-xs text-gray-500">1 day ago</p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderServices = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>System Services</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {systemServices.map((service, index) => (
              <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center">
                  <div className="h-10 w-10 bg-blue-50 rounded-lg flex items-center justify-center mr-4">
                    {service.icon}
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">{service.name}</h4>
                    <p className="text-sm text-gray-500">Uptime: {service.uptime}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="text-right">
                    <p className="text-sm text-gray-600">CPU: {service.cpu}%</p>
                    <p className="text-sm text-gray-600">Memory: {service.memory}%</p>
                  </div>
                  <Badge className={getStatusColor(service.status)}>
                    {service.status}
                  </Badge>
                  <div className="flex space-x-2">
                    <button className="text-blue-600 hover:text-blue-800 text-sm">Restart</button>
                    <button className="text-gray-600 hover:text-gray-800 text-sm">Logs</button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderMaintenance = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Scheduled Maintenance Tasks</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-medium text-gray-600">Task</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-600">Schedule</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-600">Last Run</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-600">Next Run</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-600">Status</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-600">Actions</th>
                </tr>
              </thead>
              <tbody>
                {maintenanceTasks.map((task) => (
                  <tr key={task.id} className="border-b border-gray-100 hover:bg-gray-50">
                    <td className="py-4 px-4">
                      <div>
                        <div className="font-medium text-gray-900">{task.task}</div>
                        <div className="text-sm text-gray-500">Duration: {task.duration}</div>
                      </div>
                    </td>
                    <td className="py-4 px-4 text-gray-600">{task.schedule}</td>
                    <td className="py-4 px-4 text-gray-600">{new Date(task.lastRun).toLocaleDateString()}</td>
                    <td className="py-4 px-4 text-gray-600">{new Date(task.nextRun).toLocaleDateString()}</td>
                    <td className="py-4 px-4">
                      <div className="flex items-center">
                        {getStatusIcon(task.status)}
                        <Badge className={`ml-2 ${getStatusColor(task.status)}`}>
                          {task.status}
                        </Badge>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex space-x-2">
                        <button className="text-blue-600 hover:text-blue-800 text-sm">Run Now</button>
                        <button className="text-gray-600 hover:text-gray-800 text-sm">Edit</button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderContent = () => {
    switch (activeTab) {
      case 'overview':
        return renderOverview();
      case 'services':
        return renderServices();
      case 'users':
        return <div className="text-center py-8 text-gray-500">User management coming soon...</div>;
      case 'maintenance':
        return renderMaintenance();
      case 'security':
        return <div className="text-center py-8 text-gray-500">Security settings coming soon...</div>;
      case 'performance':
        return <div className="text-center py-8 text-gray-500">Performance monitoring coming soon...</div>;
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">System Administration</h1>
          <p className="text-gray-600 mt-1">
            Monitor system health, manage services, and configure maintenance
          </p>
        </div>
        <div className="flex space-x-2">
          <button className="flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50">
            <Download className="h-4 w-4 mr-2" />
            Export Logs
          </button>
          <button className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh Status
          </button>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="h-4 w-4 mr-2" />
                {tab.label}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Content */}
      {renderContent()}
    </div>
  );
};

export default SystemAdmin;
