import {
  SolarSite,
  FlightData,
  ThermalDefect,
  Project,
  Task,
  DefectCategory,
  DefectSeverity,
  ProjectStatus,
  TaskStatus,
  WarrantyRecord,
  InsurancePolicy,
  AssetDegradation,
  AssetType
} from '../types';

/**
 * Mock data for development and testing
 * This file provides realistic sample data for all major entities
 */

export const mockSites: SolarSite[] = [
  {
    id: 'site-001',
    name: 'Desert Solar Farm Alpha',
    location: {
      latitude: 34.0522,
      longitude: -118.2437,
      address: '1234 Solar Way, Mojave, CA 93501'
    },
    capacity: 50.5,
    installationDate: '2020-03-15',
    status: 'active',
    lastInspection: '2024-01-15'
  },
  {
    id: 'site-002',
    name: 'Mountain View Solar Installation',
    location: {
      latitude: 37.4419,
      longitude: -122.1430,
      address: '5678 Energy Blvd, Mountain View, CA 94041'
    },
    capacity: 25.2,
    installationDate: '2019-08-22',
    status: 'active',
    lastInspection: '2024-01-10'
  },
  {
    id: 'site-003',
    name: 'Coastal Solar Array',
    location: {
      latitude: 32.7157,
      longitude: -117.1611,
      address: '9012 Ocean View Dr, San Diego, CA 92101'
    },
    capacity: 75.8,
    installationDate: '2021-06-10',
    status: 'maintenance',
    lastInspection: '2024-01-20'
  }
];

export const mockFlights: FlightData[] = [
  {
    id: 'flight-001',
    siteId: 'site-001',
    date: '2024-01-15',
    pilot: 'John Smith',
    weather: {
      temperature: 22,
      windSpeed: 8,
      humidity: 35,
      conditions: 'Clear'
    },
    equipment: {
      drone: 'DJI Matrice 300 RTK',
      camera: 'FLIR Vue TZ20',
      resolution: '640x512'
    },
    status: 'completed'
  },
  {
    id: 'flight-002',
    siteId: 'site-001',
    date: '2024-01-10',
    pilot: 'Sarah Johnson',
    weather: {
      temperature: 18,
      windSpeed: 12,
      humidity: 42,
      conditions: 'Partly Cloudy'
    },
    equipment: {
      drone: 'DJI Matrice 300 RTK',
      camera: 'FLIR Vue TZ20',
      resolution: '640x512'
    },
    status: 'completed'
  },
  {
    id: 'flight-003',
    siteId: 'site-002',
    date: '2024-01-12',
    pilot: 'Mike Chen',
    weather: {
      temperature: 25,
      windSpeed: 5,
      humidity: 28,
      conditions: 'Clear'
    },
    equipment: {
      drone: 'DJI Matrice 300 RTK',
      camera: 'FLIR Vue TZ20',
      resolution: '640x512'
    },
    status: 'completed'
  }
];

export const mockDefects: ThermalDefect[] = [
  {
    id: 'defect-001',
    siteId: 'site-001',
    flightId: 'flight-001',
    category: 'hotspot',
    severity: 'high',
    temperature: {
      max: 85.2,
      min: 78.5,
      average: 81.8,
      ambient: 22.0
    },
    location: {
      x: 150,
      y: 200,
      panel: 'A-15-23',
      section: 'Block A'
    },
    impact: {
      powerLoss: 2.5,
      efficiency: 15.2,
      riskScore: 8
    },
    repairCost: {
      estimated: 1250,
      labor: 800,
      materials: 350,
      equipment: 100
    },
    detectedDate: '2024-01-15',
    description: 'Severe hotspot detected in panel junction box area',
    images: ['thermal_001.jpg', 'visual_001.jpg']
  },
  {
    id: 'defect-002',
    siteId: 'site-001',
    flightId: 'flight-001',
    category: 'soiling',
    severity: 'medium',
    temperature: {
      max: 45.8,
      min: 42.1,
      average: 43.9,
      ambient: 22.0
    },
    location: {
      x: 300,
      y: 150,
      panel: 'B-08-12',
      section: 'Block B'
    },
    impact: {
      powerLoss: 0.8,
      efficiency: 5.2,
      riskScore: 4
    },
    repairCost: {
      estimated: 150,
      labor: 100,
      materials: 30,
      equipment: 20
    },
    detectedDate: '2024-01-15',
    description: 'Heavy soiling reducing panel efficiency'
  },
  {
    id: 'defect-003',
    siteId: 'site-001',
    flightId: 'flight-001',
    category: 'cracking',
    severity: 'critical',
    temperature: {
      max: 92.1,
      min: 88.3,
      average: 90.2,
      ambient: 22.0
    },
    location: {
      x: 450,
      y: 300,
      panel: 'C-22-05',
      section: 'Block C'
    },
    impact: {
      powerLoss: 4.2,
      efficiency: 25.8,
      riskScore: 9
    },
    repairCost: {
      estimated: 2800,
      labor: 1500,
      materials: 1100,
      equipment: 200
    },
    detectedDate: '2024-01-15',
    description: 'Severe cell cracking with potential safety hazard'
  },
  {
    id: 'defect-004',
    siteId: 'site-002',
    flightId: 'flight-003',
    category: 'connection_issue',
    severity: 'medium',
    temperature: {
      max: 68.5,
      min: 65.2,
      average: 66.8,
      ambient: 25.0
    },
    location: {
      x: 200,
      y: 180,
      panel: 'D-10-15',
      section: 'Block D'
    },
    impact: {
      powerLoss: 1.5,
      efficiency: 8.7,
      riskScore: 6
    },
    repairCost: {
      estimated: 650,
      labor: 400,
      materials: 200,
      equipment: 50
    },
    detectedDate: '2024-01-12',
    description: 'Loose connection causing increased resistance'
  }
];

export const mockProjects: Project[] = [
  {
    id: 'project-001',
    name: 'Q1 2024 Critical Repairs - Desert Solar Farm Alpha',
    description: 'Address critical and high-severity defects identified in January inspection',
    siteId: 'site-001',
    status: 'in_progress',
    priority: 'high',
    startDate: '2024-01-20',
    estimatedEndDate: '2024-02-15',
    budget: {
      allocated: 15000,
      spent: 4200,
      remaining: 10800
    },
    team: [
      {
        id: 'user-001',
        name: 'Alex Rodriguez',
        role: 'Project Manager',
        email: '<EMAIL>',
        phone: '******-0101'
      },
      {
        id: 'user-002',
        name: 'Maria Garcia',
        role: 'Lead Technician',
        email: '<EMAIL>',
        phone: '******-0102'
      }
    ],
    defects: ['defect-001', 'defect-003'],
    progress: 35,
    createdBy: 'user-001',
    createdDate: '2024-01-18',
    lastUpdated: '2024-01-25'
  },
  {
    id: 'project-002',
    name: 'Routine Maintenance - Mountain View',
    description: 'Regular maintenance and minor repairs for Mountain View installation',
    siteId: 'site-002',
    status: 'planning',
    priority: 'medium',
    startDate: '2024-02-01',
    estimatedEndDate: '2024-02-10',
    budget: {
      allocated: 5000,
      spent: 0,
      remaining: 5000
    },
    team: [
      {
        id: 'user-003',
        name: 'David Kim',
        role: 'Project Manager',
        email: '<EMAIL>'
      }
    ],
    defects: ['defect-004'],
    progress: 0,
    createdBy: 'user-003',
    createdDate: '2024-01-22',
    lastUpdated: '2024-01-22'
  }
];

export const mockTasks: Task[] = [
  {
    id: 'task-001',
    projectId: 'project-001',
    defectId: 'defect-001',
    title: 'Replace junction box for panel A-15-23',
    description: 'Replace faulty junction box causing hotspot in panel A-15-23',
    status: 'in_progress',
    priority: 'high',
    assignedTo: 'user-002',
    estimatedHours: 4,
    actualHours: 2.5,
    startDate: '2024-01-22',
    dueDate: '2024-01-24',
    dependencies: [],
    tags: ['electrical', 'safety'],
    notes: 'Ordered replacement junction box, arriving tomorrow'
  },
  {
    id: 'task-002',
    projectId: 'project-001',
    defectId: 'defect-003',
    title: 'Replace cracked panel C-22-05',
    description: 'Complete panel replacement due to severe cracking',
    status: 'not_started',
    priority: 'urgent',
    assignedTo: 'user-002',
    estimatedHours: 6,
    dueDate: '2024-01-28',
    dependencies: ['task-001'],
    tags: ['replacement', 'safety', 'critical'],
    notes: 'Waiting for panel delivery and completion of junction box repair'
  },
  {
    id: 'task-003',
    projectId: 'project-002',
    defectId: 'defect-004',
    title: 'Tighten connections for panel D-10-15',
    description: 'Inspect and tighten loose electrical connections',
    status: 'not_started',
    priority: 'medium',
    assignedTo: 'user-003',
    estimatedHours: 2,
    dueDate: '2024-02-03',
    dependencies: [],
    tags: ['electrical', 'maintenance']
  }
];

export const mockWarranties: WarrantyRecord[] = [
  {
    id: 'warranty-001',
    siteId: 'site-001',
    componentType: 'solar_panel',
    componentId: 'panel-A-15-23',
    componentName: 'Solar Panel A-15-23',
    manufacturer: 'SunPower',
    model: 'SPR-X22-370',
    serialNumber: 'SP2023001234',
    purchaseDate: '2020-01-15',
    installationDate: '2020-03-15',
    warrantyType: 'manufacturer',
    warrantyPeriod: 25,
    warrantyStartDate: '2020-03-15',
    warrantyEndDate: '2045-03-15',
    status: 'active',
    coverage: {
      parts: true,
      labor: true,
      performance: true,
      defects: true
    },
    documents: [
      {
        id: 'doc-001',
        type: 'warranty_certificate',
        name: 'SunPower_Warranty_Certificate.pdf',
        url: '/documents/warranty-001-cert.pdf',
        uploadDate: '2020-03-15',
        size: 245760
      },
      {
        id: 'doc-002',
        type: 'purchase_invoice',
        name: 'Purchase_Invoice_SP001.pdf',
        url: '/documents/warranty-001-invoice.pdf',
        uploadDate: '2020-01-15',
        size: 156432
      }
    ],
    claims: [
      {
        id: 'claim-001',
        warrantyId: 'warranty-001',
        defectId: 'defect-001',
        claimDate: '2024-01-16',
        description: 'Hotspot defect in junction box area',
        status: 'submitted',
        documents: []
      }
    ],
    notes: 'Panel showing hotspot issues, claim submitted to manufacturer',
    createdDate: '2020-03-15',
    lastUpdated: '2024-01-16'
  },
  {
    id: 'warranty-002',
    siteId: 'site-001',
    componentType: 'inverter',
    componentId: 'inverter-001',
    componentName: 'Central Inverter Unit 1',
    manufacturer: 'SMA Solar',
    model: 'Sunny Central 2500-EV',
    serialNumber: 'SMA2024567890',
    purchaseDate: '2020-02-01',
    installationDate: '2020-03-20',
    warrantyType: 'manufacturer',
    warrantyPeriod: 10,
    warrantyStartDate: '2020-03-20',
    warrantyEndDate: '2030-03-20',
    status: 'active',
    coverage: {
      parts: true,
      labor: true,
      performance: false,
      defects: true
    },
    documents: [
      {
        id: 'doc-003',
        type: 'warranty_certificate',
        name: 'SMA_Inverter_Warranty.pdf',
        url: '/documents/warranty-002-cert.pdf',
        uploadDate: '2020-03-20',
        size: 198765
      }
    ],
    claims: [],
    createdDate: '2020-03-20',
    lastUpdated: '2020-03-20'
  },
  {
    id: 'warranty-003',
    siteId: 'site-001',
    componentType: 'mounting_system',
    componentId: 'mounting-block-a',
    componentName: 'Mounting System Block A',
    manufacturer: 'Unirac',
    model: 'SolarMount Pro',
    purchaseDate: '2020-01-20',
    installationDate: '2020-03-10',
    warrantyType: 'installer',
    warrantyPeriod: 20,
    warrantyStartDate: '2020-03-10',
    warrantyEndDate: '2040-03-10',
    status: 'active',
    coverage: {
      parts: true,
      labor: true,
      performance: false,
      defects: true
    },
    documents: [
      {
        id: 'doc-004',
        type: 'installation_certificate',
        name: 'Installation_Certificate_BlockA.pdf',
        url: '/documents/warranty-003-install.pdf',
        uploadDate: '2020-03-10',
        size: 134567
      }
    ],
    claims: [],
    createdDate: '2020-03-10',
    lastUpdated: '2020-03-10'
  },
  {
    id: 'warranty-004',
    siteId: 'site-002',
    componentType: 'solar_panel',
    componentId: 'panel-D-10-15',
    componentName: 'Solar Panel D-10-15',
    manufacturer: 'Canadian Solar',
    model: 'CS6K-280MS',
    serialNumber: 'CS2019876543',
    purchaseDate: '2019-06-01',
    installationDate: '2019-08-22',
    warrantyType: 'manufacturer',
    warrantyPeriod: 25,
    warrantyStartDate: '2019-08-22',
    warrantyEndDate: '2044-08-22',
    status: 'active',
    coverage: {
      parts: true,
      labor: false,
      performance: true,
      defects: true
    },
    documents: [
      {
        id: 'doc-005',
        type: 'warranty_certificate',
        name: 'CanadianSolar_Warranty.pdf',
        url: '/documents/warranty-004-cert.pdf',
        uploadDate: '2019-08-22',
        size: 223456
      }
    ],
    claims: [],
    createdDate: '2019-08-22',
    lastUpdated: '2019-08-22'
  },
  {
    id: 'warranty-005',
    siteId: 'site-001',
    componentType: 'solar_panel',
    componentId: 'panel-expired',
    componentName: 'Legacy Panel Block E',
    manufacturer: 'FirstSolar',
    model: 'FS-280',
    purchaseDate: '2015-01-01',
    installationDate: '2015-03-01',
    warrantyType: 'manufacturer',
    warrantyPeriod: 8,
    warrantyStartDate: '2015-03-01',
    warrantyEndDate: '2023-03-01',
    status: 'expired',
    coverage: {
      parts: true,
      labor: false,
      performance: false,
      defects: true
    },
    documents: [],
    claims: [],
    notes: 'Warranty expired, consider replacement or extended warranty',
    createdDate: '2015-03-01',
    lastUpdated: '2023-03-01'
  }
];

export const mockInsurancePolicies: InsurancePolicy[] = [
  {
    id: 'policy-001',
    siteId: 'site-001',
    policyNumber: 'POL-2024-001-SOLAR',
    provider: 'Solar Insurance Corp',
    policyType: 'comprehensive',
    coverageType: 'site_level',
    coverageAmount: 5000000,
    premium: {
      annual: 45000,
      monthly: 3750,
      paymentFrequency: 'monthly'
    },
    deductible: 25000,
    effectiveDate: '2024-01-01',
    expirationDate: '2024-12-31',
    status: 'active',
    coverage: {
      propertyDamage: true,
      businessInterruption: true,
      equipmentBreakdown: true,
      naturalDisasters: true,
      theft: true,
      liability: true
    },
    exclusions: [
      'Nuclear incidents',
      'War and terrorism',
      'Intentional damage',
      'Normal wear and tear'
    ],
    documents: [
      {
        id: 'ins-doc-001',
        type: 'policy_certificate',
        name: 'Comprehensive_Policy_Certificate.pdf',
        url: '/documents/policy-001-cert.pdf',
        uploadDate: '2024-01-01',
        size: 567890
      },
      {
        id: 'ins-doc-002',
        type: 'premium_invoice',
        name: 'Premium_Invoice_Jan2024.pdf',
        url: '/documents/policy-001-invoice.pdf',
        uploadDate: '2024-01-01',
        size: 123456
      }
    ],
    claims: [],
    renewalDate: '2024-11-01',
    autoRenewal: true,
    notes: 'Comprehensive coverage for entire site including business interruption',
    createdDate: '2024-01-01',
    lastUpdated: '2024-01-01'
  },
  {
    id: 'policy-002',
    siteId: 'site-002',
    policyNumber: 'POL-2023-002-EQUIP',
    provider: 'GreenTech Insurance',
    policyType: 'equipment',
    coverageType: 'component_level',
    coverageAmount: 2500000,
    premium: {
      annual: 18000,
      paymentFrequency: 'annually'
    },
    deductible: 10000,
    effectiveDate: '2023-06-01',
    expirationDate: '2024-05-31',
    status: 'pending_renewal',
    coverage: {
      propertyDamage: false,
      businessInterruption: false,
      equipmentBreakdown: true,
      naturalDisasters: true,
      theft: true,
      liability: false
    },
    exclusions: [
      'Flood damage',
      'Earthquake damage above 6.0 magnitude',
      'Cyber attacks'
    ],
    documents: [
      {
        id: 'ins-doc-003',
        type: 'policy_certificate',
        name: 'Equipment_Policy_2023.pdf',
        url: '/documents/policy-002-cert.pdf',
        uploadDate: '2023-06-01',
        size: 345678
      }
    ],
    claims: [
      {
        id: 'ins-claim-001',
        policyId: 'policy-002',
        defectId: 'defect-004',
        claimNumber: 'CLM-2024-001',
        incidentDate: '2024-01-12',
        claimDate: '2024-01-15',
        description: 'Equipment failure due to connection issues',
        claimType: 'equipment_failure',
        estimatedAmount: 15000,
        approvedAmount: 12000,
        status: 'approved',
        adjuster: 'Jane Smith, GreenTech Insurance',
        resolution: 'Approved for equipment repair and replacement',
        settlementDate: '2024-01-25',
        documents: [],
        notes: 'Quick settlement for routine equipment failure'
      }
    ],
    renewalDate: '2024-04-01',
    autoRenewal: false,
    notes: 'Renewal pending - need to review coverage limits',
    createdDate: '2023-06-01',
    lastUpdated: '2024-01-25'
  },
  {
    id: 'policy-003',
    siteId: 'site-003',
    policyNumber: 'POL-2021-003-PROP',
    provider: 'Renewable Risk Solutions',
    policyType: 'property',
    coverageType: 'site_level',
    coverageAmount: 7500000,
    premium: {
      annual: 62000,
      paymentFrequency: 'quarterly'
    },
    deductible: 50000,
    effectiveDate: '2021-06-10',
    expirationDate: '2025-06-10',
    status: 'active',
    coverage: {
      propertyDamage: true,
      businessInterruption: false,
      equipmentBreakdown: false,
      naturalDisasters: true,
      theft: true,
      liability: true
    },
    exclusions: [
      'Acts of God beyond specified natural disasters',
      'Government seizure',
      'Nuclear contamination'
    ],
    documents: [
      {
        id: 'ins-doc-004',
        type: 'policy_certificate',
        name: 'Property_Policy_2021.pdf',
        url: '/documents/policy-003-cert.pdf',
        uploadDate: '2021-06-10',
        size: 456789
      }
    ],
    claims: [],
    renewalDate: '2025-04-10',
    autoRenewal: true,
    createdDate: '2021-06-10',
    lastUpdated: '2021-06-10'
  }
];

export const mockAssetDegradation: AssetDegradation[] = [
  {
    id: 'asset-deg-001',
    siteId: 'site-001',
    assetId: 'panel-A-15-23',
    assetType: 'solar_panel',
    assetName: 'Solar Panel A-15-23',
    manufacturer: 'SunPower',
    model: 'SPR-X22-370',
    serialNumber: 'SP2023001234',
    installationDate: '2020-03-15',
    warrantyEndDate: '2045-03-15',
    currentAge: 4.8,
    expectedLifespan: 25,
    remainingLife: 20.2,
    degradationRate: {
      annual: 0.45,
      cumulative: 2.16,
      projected: 11.25
    },
    performanceMetrics: {
      initialRating: 370,
      currentRating: 362,
      degradationLoss: 8,
      projectedEndOfLife: 328.4
    },
    riskFactors: {
      environmental: 6,
      operational: 4,
      maintenance: 3,
      overall: 4.3
    },
    lastAssessment: '2024-01-15',
    nextAssessment: '2024-07-15',
    notes: 'Performing within expected parameters'
  },
  {
    id: 'asset-deg-002',
    siteId: 'site-001',
    assetId: 'inverter-001',
    assetType: 'inverter',
    assetName: 'Central Inverter Unit 1',
    manufacturer: 'SMA Solar',
    model: 'Sunny Central 2500-EV',
    serialNumber: 'SMA2024567890',
    installationDate: '2020-03-20',
    warrantyEndDate: '2030-03-20',
    currentAge: 4.8,
    expectedLifespan: 15,
    remainingLife: 10.2,
    degradationRate: {
      annual: 0.8,
      cumulative: 3.84,
      projected: 12.0
    },
    performanceMetrics: {
      initialRating: 2500,
      currentRating: 2404,
      degradationLoss: 96,
      projectedEndOfLife: 2200
    },
    riskFactors: {
      environmental: 5,
      operational: 6,
      maintenance: 4,
      overall: 5.0
    },
    lastAssessment: '2024-01-10',
    nextAssessment: '2024-04-10',
    notes: 'Higher than expected degradation, monitor closely'
  },
  {
    id: 'asset-deg-003',
    siteId: 'site-002',
    assetId: 'panel-D-10-15',
    assetType: 'solar_panel',
    assetName: 'Solar Panel D-10-15',
    manufacturer: 'Canadian Solar',
    model: 'CS6K-280MS',
    serialNumber: 'CS2019876543',
    installationDate: '2019-08-22',
    warrantyEndDate: '2044-08-22',
    currentAge: 5.4,
    expectedLifespan: 25,
    remainingLife: 19.6,
    degradationRate: {
      annual: 0.5,
      cumulative: 2.7,
      projected: 12.5
    },
    performanceMetrics: {
      initialRating: 280,
      currentRating: 272.4,
      degradationLoss: 7.6,
      projectedEndOfLife: 245
    },
    riskFactors: {
      environmental: 7,
      operational: 5,
      maintenance: 4,
      overall: 5.3
    },
    lastAssessment: '2024-01-12',
    nextAssessment: '2024-07-12'
  },
  {
    id: 'asset-deg-004',
    siteId: 'site-001',
    assetId: 'mounting-block-a',
    assetType: 'mounting_system',
    assetName: 'Mounting System Block A',
    manufacturer: 'Unirac',
    model: 'SolarMount Pro',
    installationDate: '2020-03-10',
    currentAge: 4.8,
    expectedLifespan: 30,
    remainingLife: 25.2,
    degradationRate: {
      annual: 0.2,
      cumulative: 0.96,
      projected: 6.0
    },
    performanceMetrics: {
      initialRating: 100,
      currentRating: 99.04,
      degradationLoss: 0.96,
      projectedEndOfLife: 94
    },
    riskFactors: {
      environmental: 4,
      operational: 2,
      maintenance: 2,
      overall: 2.7
    },
    lastAssessment: '2024-01-05',
    nextAssessment: '2025-01-05',
    notes: 'Excellent condition, minimal degradation'
  },
  {
    id: 'asset-deg-005',
    siteId: 'site-003',
    assetId: 'legacy-panel-001',
    assetType: 'solar_panel',
    assetName: 'Legacy Panel Block E-01',
    manufacturer: 'FirstSolar',
    model: 'FS-280',
    installationDate: '2015-03-01',
    currentAge: 9.8,
    expectedLifespan: 20,
    remainingLife: 10.2,
    degradationRate: {
      annual: 0.7,
      cumulative: 6.86,
      projected: 14.0
    },
    performanceMetrics: {
      initialRating: 280,
      currentRating: 260.8,
      degradationLoss: 19.2,
      projectedEndOfLife: 240.8
    },
    riskFactors: {
      environmental: 8,
      operational: 7,
      maintenance: 6,
      overall: 7.0
    },
    lastAssessment: '2023-12-15',
    nextAssessment: '2024-06-15',
    notes: 'Aging asset, consider replacement planning'
  },
  {
    id: 'asset-deg-006',
    siteId: 'site-001',
    assetId: 'battery-001',
    assetType: 'battery',
    assetName: 'Battery Storage Unit 1',
    manufacturer: 'Tesla',
    model: 'Powerpack 2',
    serialNumber: 'TS2021445566',
    installationDate: '2021-06-15',
    warrantyEndDate: '2031-06-15',
    currentAge: 2.6,
    expectedLifespan: 12,
    remainingLife: 9.4,
    degradationRate: {
      annual: 2.5,
      cumulative: 6.5,
      projected: 30.0
    },
    performanceMetrics: {
      initialRating: 210,
      currentRating: 196.35,
      degradationLoss: 13.65,
      projectedEndOfLife: 147
    },
    riskFactors: {
      environmental: 5,
      operational: 8,
      maintenance: 6,
      overall: 6.3
    },
    lastAssessment: '2024-01-20',
    nextAssessment: '2024-04-20',
    notes: 'Battery degradation within normal parameters'
  },
  {
    id: 'asset-deg-007',
    siteId: 'site-002',
    assetId: 'transformer-001',
    assetType: 'transformer',
    assetName: 'Main Transformer Unit',
    manufacturer: 'ABB',
    model: 'DryPower 2500',
    serialNumber: 'ABB2020998877',
    installationDate: '2019-07-10',
    currentAge: 5.5,
    expectedLifespan: 35,
    remainingLife: 29.5,
    degradationRate: {
      annual: 0.15,
      cumulative: 0.825,
      projected: 5.25
    },
    performanceMetrics: {
      initialRating: 2500,
      currentRating: 2479.4,
      degradationLoss: 20.6,
      projectedEndOfLife: 2368.75
    },
    riskFactors: {
      environmental: 3,
      operational: 3,
      maintenance: 2,
      overall: 2.7
    },
    lastAssessment: '2024-01-08',
    nextAssessment: '2025-01-08',
    notes: 'Transformer performing excellently'
  }
];

/**
 * Generate additional mock defects for testing
 */
export const generateMockDefects = (count: number, siteId: string, flightId: string): ThermalDefect[] => {
  const categories: DefectCategory[] = ['hotspot', 'soiling', 'shading', 'cracking', 'corrosion', 'connection_issue'];
  const severities: DefectSeverity[] = ['low', 'medium', 'high', 'critical'];

  return Array.from({ length: count }, (_, index) => ({
    id: `defect-gen-${index + 1}`,
    siteId,
    flightId,
    category: categories[Math.floor(Math.random() * categories.length)],
    severity: severities[Math.floor(Math.random() * severities.length)],
    temperature: {
      max: 40 + Math.random() * 50,
      min: 35 + Math.random() * 40,
      average: 37.5 + Math.random() * 45,
      ambient: 20 + Math.random() * 10
    },
    location: {
      x: Math.floor(Math.random() * 800),
      y: Math.floor(Math.random() * 600),
      panel: `${String.fromCharCode(65 + Math.floor(Math.random() * 5))}-${String(Math.floor(Math.random() * 30) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 30) + 1).padStart(2, '0')}`,
      section: `Block ${String.fromCharCode(65 + Math.floor(Math.random() * 5))}`
    },
    impact: {
      powerLoss: Math.random() * 5,
      efficiency: Math.random() * 30,
      riskScore: Math.floor(Math.random() * 10) + 1
    },
    repairCost: {
      estimated: Math.floor(Math.random() * 3000) + 100,
      labor: Math.floor(Math.random() * 1500) + 50,
      materials: Math.floor(Math.random() * 1000) + 30,
      equipment: Math.floor(Math.random() * 500) + 20
    },
    detectedDate: new Date().toISOString().split('T')[0],
    description: `Generated defect for testing purposes`
  }));
};
