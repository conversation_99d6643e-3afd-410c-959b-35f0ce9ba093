// Core domain types
export interface SolarSite {
  id: string;
  name: string;
  location: {
    latitude: number;
    longitude: number;
    address: string;
  };
  capacity: number; // MW
  installationDate: string;
  status: 'active' | 'maintenance' | 'offline';
  lastInspection?: string;
}

export interface FlightData {
  id: string;
  siteId: string;
  date: string;
  pilot: string;
  weather: {
    temperature: number;
    windSpeed: number;
    humidity: number;
    conditions: string;
  };
  equipment: {
    drone: string;
    camera: string;
    resolution: string;
  };
  status: 'completed' | 'processing' | 'failed';
}

export interface ThermalDefect {
  id: string;
  siteId: string;
  flightId: string;
  category: DefectCategory;
  severity: DefectSeverity;
  temperature: {
    max: number;
    min: number;
    average: number;
    ambient: number;
  };
  location: {
    x: number;
    y: number;
    panel?: string;
    section?: string;
  };
  impact: {
    powerLoss: number; // kW
    efficiency: number; // percentage
    riskScore: number; // 1-10
  };
  repairCost: {
    estimated: number;
    labor: number;
    materials: number;
    equipment: number;
  };
  detectedDate: string;
  description?: string;
  images?: string[];
}

export type DefectCategory = 
  | 'hotspot'
  | 'soiling'
  | 'shading'
  | 'cracking'
  | 'corrosion'
  | 'connection_issue'
  | 'bypass_diode'
  | 'other';

export type DefectSeverity = 'low' | 'medium' | 'high' | 'critical';

// Optimization types
export interface OptimizationConfig {
  maxRepairCost: number;
  maxNumberOfRepairs: number;
  strategy: OptimizationStrategy;
  timeframe: number; // days
  priorityWeights: {
    impact: number;
    cost: number;
    risk: number;
  };
}

export type OptimizationStrategy = 
  | 'maximize_impact'
  | 'minimize_cost'
  | 'minimize_risk'
  | 'balanced';

export interface OptimizationResult {
  selectedDefects: string[];
  totalCost: number;
  totalImpact: number;
  totalRisk: number;
  roi: number;
  timeline: number; // days
  summary: {
    defectsAddressed: number;
    powerRecovered: number; // kW
    costPerKW: number;
  };
}

// Project Management types
export interface Project {
  id: string;
  name: string;
  description: string;
  siteId: string;
  status: ProjectStatus;
  priority: ProjectPriority;
  startDate: string;
  endDate?: string;
  estimatedEndDate: string;
  budget: {
    allocated: number;
    spent: number;
    remaining: number;
  };
  team: ProjectMember[];
  defects: string[]; // defect IDs
  progress: number; // 0-100
  createdBy: string;
  createdDate: string;
  lastUpdated: string;
}

export type ProjectStatus = 
  | 'planning'
  | 'in_progress'
  | 'on_hold'
  | 'completed'
  | 'cancelled';

export type ProjectPriority = 'low' | 'medium' | 'high' | 'urgent';

export interface ProjectMember {
  id: string;
  name: string;
  role: string;
  email: string;
  phone?: string;
}

export interface Task {
  id: string;
  projectId: string;
  defectId?: string;
  title: string;
  description: string;
  status: TaskStatus;
  priority: TaskPriority;
  assignedTo: string;
  estimatedHours: number;
  actualHours?: number;
  startDate?: string;
  dueDate: string;
  completedDate?: string;
  dependencies: string[]; // task IDs
  tags: string[];
  notes?: string;
}

export type TaskStatus = 
  | 'not_started'
  | 'in_progress'
  | 'blocked'
  | 'completed'
  | 'cancelled';

export type TaskPriority = 'low' | 'medium' | 'high' | 'urgent';

// UI and component types
export interface NavItem {
  title: string;
  icon: string;
  path: string;
  active?: boolean;
  badge?: number;
}

export interface ChartDataPoint {
  x: number;
  y: number;
  label?: string;
  color?: string;
  size?: number;
}

export interface SummaryStats {
  totalDefects: number;
  criticalDefects: number;
  totalPowerLoss: number;
  averageTemperature: number;
  estimatedRepairCost: number;
  defectsByCategory: Record<DefectCategory, number>;
  defectsBySeverity: Record<DefectSeverity, number>;
}

// API response types
export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  timestamp: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Form and validation types
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'number' | 'select' | 'date' | 'textarea';
  required?: boolean;
  options?: { value: string; label: string }[];
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
  };
}

export interface LoadingState {
  isLoading: boolean;
  error?: string;
}

// Warranty Management types
export interface WarrantyRecord {
  id: string;
  siteId: string;
  componentType: WarrantyComponentType;
  componentId: string;
  componentName: string;
  manufacturer: string;
  model: string;
  serialNumber?: string;
  purchaseDate: string;
  installationDate: string;
  warrantyType: WarrantyType;
  warrantyPeriod: number; // years
  warrantyStartDate: string;
  warrantyEndDate: string;
  status: WarrantyStatus;
  coverage: {
    parts: boolean;
    labor: boolean;
    performance: boolean;
    defects: boolean;
  };
  documents: WarrantyDocument[];
  claims: WarrantyClaim[];
  notes?: string;
  createdDate: string;
  lastUpdated: string;
}

export type WarrantyComponentType =
  | 'solar_panel'
  | 'inverter'
  | 'mounting_system'
  | 'electrical_components'
  | 'monitoring_system'
  | 'other';

export type WarrantyType =
  | 'manufacturer'
  | 'installer'
  | 'extended'
  | 'performance'
  | 'workmanship';

export type WarrantyStatus =
  | 'active'
  | 'expired'
  | 'claimed'
  | 'voided'
  | 'transferred';

export interface WarrantyDocument {
  id: string;
  type: 'warranty_certificate' | 'purchase_invoice' | 'installation_certificate' | 'other';
  name: string;
  url: string;
  uploadDate: string;
  size: number;
}

export interface WarrantyClaim {
  id: string;
  warrantyId: string;
  defectId?: string;
  claimDate: string;
  description: string;
  status: 'submitted' | 'under_review' | 'approved' | 'rejected' | 'completed';
  claimAmount?: number;
  resolution?: string;
  completedDate?: string;
  documents: WarrantyDocument[];
}

// Insurance Management types
export interface InsurancePolicy {
  id: string;
  siteId: string;
  policyNumber: string;
  provider: string;
  policyType: InsurancePolicyType;
  coverageType: InsuranceCoverageType;
  coverageAmount: number;
  premium: {
    annual: number;
    monthly?: number;
    paymentFrequency: 'monthly' | 'quarterly' | 'annually';
  };
  deductible: number;
  effectiveDate: string;
  expirationDate: string;
  status: InsurancePolicyStatus;
  coverage: {
    propertyDamage: boolean;
    businessInterruption: boolean;
    equipmentBreakdown: boolean;
    naturalDisasters: boolean;
    theft: boolean;
    liability: boolean;
  };
  exclusions: string[];
  documents: InsuranceDocument[];
  claims: InsuranceClaim[];
  renewalDate: string;
  autoRenewal: boolean;
  notes?: string;
  createdDate: string;
  lastUpdated: string;
}

export type InsurancePolicyType =
  | 'property'
  | 'equipment'
  | 'business_interruption'
  | 'liability'
  | 'comprehensive';

export type InsuranceCoverageType =
  | 'site_level'
  | 'component_level'
  | 'system_wide';

export type InsurancePolicyStatus =
  | 'active'
  | 'expired'
  | 'cancelled'
  | 'pending_renewal'
  | 'lapsed';

export interface InsuranceDocument {
  id: string;
  type: 'policy_certificate' | 'premium_invoice' | 'claim_form' | 'assessment_report' | 'other';
  name: string;
  url: string;
  uploadDate: string;
  size: number;
}

export interface InsuranceClaim {
  id: string;
  policyId: string;
  defectId?: string;
  claimNumber: string;
  incidentDate: string;
  claimDate: string;
  description: string;
  claimType: 'property_damage' | 'equipment_failure' | 'business_interruption' | 'liability' | 'other';
  estimatedAmount: number;
  approvedAmount?: number;
  status: 'submitted' | 'under_investigation' | 'approved' | 'rejected' | 'settled' | 'closed';
  adjuster?: string;
  resolution?: string;
  settlementDate?: string;
  documents: InsuranceDocument[];
  notes?: string;
}

// Asset Degradation types
export interface AssetDegradation {
  id: string;
  siteId: string;
  assetId: string;
  assetType: AssetType;
  assetName: string;
  manufacturer: string;
  model: string;
  serialNumber?: string;
  installationDate: string;
  warrantyEndDate?: string;
  currentAge: number; // years
  expectedLifespan: number; // years
  remainingLife: number; // years
  degradationRate: {
    annual: number; // percentage per year
    cumulative: number; // total degradation percentage
    projected: number; // projected at end of life
  };
  performanceMetrics: {
    initialRating: number; // kW or efficiency %
    currentRating: number;
    degradationLoss: number; // kW or efficiency % lost
    projectedEndOfLife: number;
  };
  riskFactors: {
    environmental: number; // 1-10 scale
    operational: number;
    maintenance: number;
    overall: number;
  };
  lastAssessment: string;
  nextAssessment: string;
  notes?: string;
}

export type AssetType =
  | 'solar_panel'
  | 'inverter'
  | 'battery'
  | 'mounting_system'
  | 'electrical_components'
  | 'monitoring_system'
  | 'transformer'
  | 'combiner_box';

export interface DegradationSummary {
  totalAssets: number;
  averageAge: number;
  oldestAsset: {
    id: string;
    name: string;
    age: number;
    type: AssetType;
  };
  projectedPerformanceLoss: {
    nextYear: number;
    next5Years: number;
    endOfLife: number;
  };
  assetsByAge: {
    range: string;
    count: number;
    percentage: number;
  }[];
  degradationByType: {
    type: AssetType;
    averageDegradation: number;
    count: number;
  }[];
  riskDistribution: {
    low: number;
    medium: number;
    high: number;
    critical: number;
  };
}

export interface DegradationCurvePoint {
  year: number;
  expectedDegradation: number;
  actualDegradation?: number;
  projectedDegradation: number;
}

// Context types
export interface AppContextType {
  selectedSite: SolarSite | null;
  setSelectedSite: (site: SolarSite | null) => void;
  selectedFlight: FlightData | null;
  setSelectedFlight: (flight: FlightData | null) => void;
  defects: ThermalDefect[];
  setDefects: (defects: ThermalDefect[]) => void;
  loading: LoadingState;
  setLoading: (loading: LoadingState) => void;
}
